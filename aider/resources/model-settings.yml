- name: gpt-3.5-turbo
  weak_model_name: gpt-4o-mini
  reminder: sys

- name: gpt-3.5-turbo-0125
  weak_model_name: gpt-4o-mini
  reminder: sys

- name: gpt-3.5-turbo-1106
  weak_model_name: gpt-4o-mini
  reminder: sys

- name: gpt-3.5-turbo-0613
  weak_model_name: gpt-4o-mini
  reminder: sys

- name: gpt-3.5-turbo-16k-0613
  weak_model_name: gpt-4o-mini
  reminder: sys

- name: gpt-4-turbo-2024-04-09
  edit_format: udiff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  lazy: true
  reminder: sys

- name: gpt-4-turbo
  edit_format: udiff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  lazy: true
  reminder: sys

- name: openai/gpt-4o
  edit_format: diff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  lazy: true
  reminder: sys
  examples_as_sys_msg: true
  editor_edit_format: editor-diff

- name: openai/gpt-4o-2024-08-06
  edit_format: diff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  lazy: true
  reminder: sys
  examples_as_sys_msg: true

- name: gpt-4o-2024-08-06
  edit_format: diff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  lazy: true
  reminder: sys
  examples_as_sys_msg: true

- name: gpt-4o-2024-11-20
  edit_format: diff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  lazy: true
  reminder: sys
  examples_as_sys_msg: true

- name: openai/gpt-4o-2024-11-20
  edit_format: diff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  lazy: true
  reminder: sys
  examples_as_sys_msg: true

- name: gpt-4o
  edit_format: diff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  lazy: true
  reminder: sys
  examples_as_sys_msg: true
  editor_edit_format: editor-diff

- name: gpt-4o-mini
  weak_model_name: gpt-4o-mini
  lazy: true
  reminder: sys

- name: openai/gpt-4o-mini
  weak_model_name: openai/gpt-4o-mini
  lazy: true
  reminder: sys

- name: gpt-4-0125-preview
  edit_format: udiff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  lazy: true
  reminder: sys
  examples_as_sys_msg: true

- name: gpt-4-1106-preview
  edit_format: udiff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  lazy: true
  reminder: sys

- name: gpt-4-vision-preview
  edit_format: diff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  reminder: sys

- name: gpt-4-0314
  edit_format: diff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  reminder: sys
  examples_as_sys_msg: true

- name: gpt-4-0613
  edit_format: diff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  reminder: sys

- name: gpt-4-32k-0613
  edit_format: diff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  reminder: sys

- name: claude-3-opus-20240229
  edit_format: diff
  weak_model_name: claude-3-5-haiku-20241022
  use_repo_map: true

- name: openrouter/anthropic/claude-3-opus
  edit_format: diff
  weak_model_name: openrouter/anthropic/claude-3-5-haiku
  use_repo_map: true

- name: claude-3-sonnet-20240229
  weak_model_name: claude-3-5-haiku-20241022

- name: claude-3-5-sonnet-20240620
  edit_format: diff
  weak_model_name: claude-3-5-haiku-20241022
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25
    max_tokens: 8192
  cache_control: true
  editor_model_name: claude-3-5-sonnet-20240620
  editor_edit_format: editor-diff

- name: anthropic/claude-3-5-sonnet-20240620
  edit_format: diff
  weak_model_name: anthropic/claude-3-5-haiku-20241022
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25
    max_tokens: 8192
  cache_control: true
  editor_model_name: anthropic/claude-3-5-sonnet-20240620
  editor_edit_format: editor-diff

- name: anthropic/claude-3-5-sonnet-20241022
  edit_format: diff
  weak_model_name: anthropic/claude-3-5-haiku-20241022
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25
    max_tokens: 8192
  cache_control: true
  editor_model_name: anthropic/claude-3-5-sonnet-20241022
  editor_edit_format: editor-diff

- name: anthropic/claude-3-7-sonnet-20250219
  overeager: true
  edit_format: diff
  weak_model_name: anthropic/claude-3-5-haiku-20241022
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: anthropic/claude-3-7-sonnet-20250219
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: anthropic/claude-3-7-sonnet-latest
  overeager: true
  edit_format: diff
  weak_model_name: anthropic/claude-3-5-haiku-20241022
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: anthropic/claude-3-7-sonnet-latest
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: claude-3-7-sonnet-20250219
  edit_format: diff
  weak_model_name: claude-3-5-haiku-20241022
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: claude-3-7-sonnet-20250219
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: claude-3-7-sonnet-latest
  overeager: true
  edit_format: diff
  weak_model_name: claude-3-5-haiku-20241022
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: claude-3-7-sonnet-latest
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: bedrock/anthropic.claude-3-7-sonnet-20250219-v1:0
  overeager: true
  edit_format: diff
  weak_model_name: bedrock/anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: bedrock/anthropic.claude-3-7-sonnet-20250219-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0
  overeager: true
  edit_format: diff
  weak_model_name: bedrock/us.anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: bedrock/us.anthropic.claude-3-7-sonnet-20250219-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: bedrock_converse/anthropic.claude-3-7-sonnet-20250219-v1:0
  overeager: true
  edit_format: diff
  weak_model_name: bedrock_converse/anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: bedrock_converse/anthropic.claude-3-7-sonnet-20250219-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: bedrock_converse/us.anthropic.claude-3-7-sonnet-20250219-v1:0
  overeager: true
  edit_format: diff
  weak_model_name: bedrock_converse/us.anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: bedrock_converse/us.anthropic.claude-3-7-sonnet-20250219-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: vertex_ai/claude-3-7-sonnet@20250219
  overeager: true
  edit_format: diff
  weak_model_name: vertex_ai/claude-3-5-haiku@20241022
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    max_tokens: 64000
  editor_model_name: vertex_ai/claude-3-7-sonnet@20250219
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: vertex_ai/claude-3-7-sonnet@20250219
  overeager: true
  edit_format: diff
  weak_model_name: vertex_ai/claude-3-5-haiku@20241022
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    max_tokens: 64000
  editor_model_name: vertex_ai/claude-3-7-sonnet@20250219
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: openrouter/anthropic/claude-3.7-sonnet
  overeager: true
  edit_format: diff
  weak_model_name: openrouter/anthropic/claude-3-5-haiku
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: openrouter/anthropic/claude-3.7-sonnet
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: openrouter/anthropic/claude-3.7-sonnet:beta
  overeager: true
  edit_format: diff
  weak_model_name: openrouter/anthropic/claude-3-5-haiku
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: openrouter/anthropic/claude-3.7-sonnet
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: bedrock/anthropic.claude-3-5-sonnet-20241022-v2:0
  edit_format: diff
  weak_model_name: bedrock/anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25
    max_tokens: 8192
  cache_control: true
  editor_model_name: bedrock/anthropic.claude-3-5-sonnet-20241022-v2:0
  editor_edit_format: editor-diff

- name: anthropic/claude-3-5-sonnet-latest
  edit_format: diff
  weak_model_name: anthropic/claude-3-5-haiku-20241022
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25
    max_tokens: 8192
  cache_control: true
  editor_model_name: anthropic/claude-3-5-sonnet-20241022
  editor_edit_format: editor-diff

- name: claude-3-5-sonnet-20241022
  edit_format: diff
  weak_model_name: claude-3-5-haiku-20241022
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25
    max_tokens: 8192
  cache_control: true
  editor_model_name: claude-3-5-sonnet-20241022
  editor_edit_format: editor-diff

- name: anthropic/claude-3-haiku-20240307
  weak_model_name: anthropic/claude-3-haiku-20240307
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25
  cache_control: true

- name: anthropic/claude-3-5-haiku-20241022
  edit_format: diff
  weak_model_name: anthropic/claude-3-5-haiku-20241022
  use_repo_map: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25
  cache_control: true

- name: bedrock/anthropic.claude-3-5-haiku-20241022-v1:0
  edit_format: diff
  weak_model_name: bedrock/anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25
  cache_control: true

- name: claude-3-5-haiku-20241022
  edit_format: diff
  weak_model_name: claude-3-5-haiku-20241022
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25
  cache_control: true

- name: vertex_ai/claude-3-5-haiku@20241022
  edit_format: diff
  weak_model_name: vertex_ai/claude-3-5-haiku@20241022
  use_repo_map: true
  extra_params:
    max_tokens: 4096

- name: claude-3-haiku-20240307
  weak_model_name: claude-3-haiku-20240307
  examples_as_sys_msg: true
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25
  cache_control: true

- name: openrouter/anthropic/claude-3.5-sonnet
  edit_format: diff
  weak_model_name: openrouter/anthropic/claude-3-5-haiku
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    max_tokens: 8192
  cache_control: true
  editor_model_name: openrouter/anthropic/claude-3.5-sonnet
  editor_edit_format: editor-diff

- name: openrouter/anthropic/claude-3.5-sonnet:beta
  edit_format: diff
  weak_model_name: openrouter/anthropic/claude-3-5-haiku:beta
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    max_tokens: 8192
  cache_control: true
  editor_model_name: openrouter/anthropic/claude-3.5-sonnet:beta
  editor_edit_format: editor-diff

- name: vertex_ai/claude-3-5-sonnet@20240620
  edit_format: diff
  weak_model_name: vertex_ai/claude-3-5-haiku@20241022
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    max_tokens: 8192
  editor_model_name: vertex_ai/claude-3-5-sonnet@20240620
  editor_edit_format: editor-diff

- name: vertex_ai/claude-3-5-sonnet-v2@20241022
  edit_format: diff
  weak_model_name: vertex_ai/claude-3-5-haiku@20241022
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    max_tokens: 8192
  editor_model_name: vertex_ai/claude-3-5-sonnet-v2@20241022
  editor_edit_format: editor-diff

- name: vertex_ai/claude-3-opus@20240229
  edit_format: diff
  weak_model_name: vertex_ai/claude-3-5-haiku@20241022
  use_repo_map: true

- name: vertex_ai/claude-3-sonnet@20240229
  weak_model_name: vertex_ai/claude-3-5-haiku@20241022

- name: command-r-plus
  weak_model_name: command-r-plus
  use_repo_map: true

- name: command-r-08-2024
  weak_model_name: command-r-08-2024
  use_repo_map: true

- name: command-r-plus-08-2024
  weak_model_name: command-r-plus-08-2024
  use_repo_map: true

- name: groq/llama3-70b-8192
  edit_format: diff
  weak_model_name: groq/llama3-8b-8192
  examples_as_sys_msg: true

- name: openrouter/meta-llama/llama-3-70b-instruct
  edit_format: diff
  weak_model_name: openrouter/meta-llama/llama-3-70b-instruct
  examples_as_sys_msg: true

- name: gemini/gemini-1.5-pro-002
  edit_format: diff
  use_repo_map: true

- name: gemini/gemini-1.5-flash-002

- name: gemini/gemini-1.5-pro
  edit_format: diff-fenced
  use_repo_map: true

- name: gemini/gemini-1.5-pro-latest
  edit_format: diff-fenced
  use_repo_map: true

- name: gemini/gemini-exp-1206
  edit_format: diff
  use_repo_map: true

- name: gemini/gemini-2.0-flash-exp
  edit_format: diff
  use_repo_map: true

- name: gemini/gemini-2.0-flash
  edit_format: diff
  use_repo_map: true

- name: openrouter/deepseek/deepseek-r1
  edit_format: diff
  weak_model_name: openrouter/deepseek/deepseek-chat
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    max_tokens: 8192
    include_reasoning: true
  caches_by_default: true
  editor_model_name: openrouter/deepseek/deepseek-chat
  editor_edit_format: editor-diff

- name: openrouter/deepseek/deepseek-r1:free
  edit_format: diff
  weak_model_name: openrouter/deepseek/deepseek-r1:free
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    max_tokens: 8192
  caches_by_default: true

- name: openrouter/deepseek/deepseek-chat-v3-0324:free
  edit_format: diff
  weak_model_name: openrouter/deepseek/deepseek-chat-v3-0324:free
  use_repo_map: true
  examples_as_sys_msg: true
  caches_by_default: true
  use_temperature: false
  editor_model_name: openrouter/deepseek/deepseek-chat-v3-0324:free
  editor_edit_format: editor-diff
  use_temperature: false
  editor_model_name: openrouter/deepseek/deepseek-r1:free
  editor_edit_format: editor-diff

- name: deepseek/deepseek-reasoner
  edit_format: diff
  weak_model_name: deepseek/deepseek-chat
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    max_tokens: 64000
  caches_by_default: true
  use_temperature: false
  editor_model_name: deepseek/deepseek-chat
  editor_edit_format: editor-diff

- name: deepseek/deepseek-chat
  edit_format: diff
  use_repo_map: true
  reminder: sys
  examples_as_sys_msg: true
  extra_params:
    max_tokens: 8192
  caches_by_default: true

- name: openrouter/deepseek/deepseek-chat:free
  edit_format: diff
  weak_model_name: openrouter/deepseek/deepseek-chat:free
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    max_tokens: 8192
  caches_by_default: true
  use_temperature: false
  editor_model_name: openrouter/deepseek/deepseek-chat:free
  editor_edit_format: editor-diff

- name: deepseek/deepseek-coder
  edit_format: diff
  use_repo_map: true
  reminder: sys
  examples_as_sys_msg: true
  extra_params:
    max_tokens: 8192
  caches_by_default: true

- name: deepseek-chat
  edit_format: diff
  use_repo_map: true
  reminder: sys
  examples_as_sys_msg: true
  extra_params:
    max_tokens: 8192

- name: deepseek-coder
  edit_format: diff
  use_repo_map: true
  reminder: sys
  examples_as_sys_msg: true
  extra_params:
    max_tokens: 8192
  caches_by_default: true

- name: openrouter/deepseek/deepseek-coder
  edit_format: diff
  use_repo_map: true
  reminder: sys
  examples_as_sys_msg: true

- name: openrouter/deepseek/deepseek-chat
  edit_format: diff
  use_repo_map: true
  reminder: sys
  examples_as_sys_msg: true

- name: openrouter/deepseek/deepseek-chat-v3-0324
  edit_format: diff
  use_repo_map: true
  reminder: sys
  examples_as_sys_msg: true
  extra_params:
    max_tokens: 8192
  caches_by_default: true

- name: openrouter/openai/gpt-4o
  edit_format: diff
  weak_model_name: openrouter/openai/gpt-4o-mini
  use_repo_map: true
  lazy: true
  reminder: sys
  examples_as_sys_msg: true
  editor_edit_format: editor-diff

- name: openai/o1-mini
  weak_model_name: openai/gpt-4o-mini
  use_repo_map: true
  use_system_prompt: false
  use_temperature: false
  editor_model_name: openai/gpt-4o
  editor_edit_format: editor-diff

- name: azure/o1-mini
  weak_model_name: azure/gpt-4o-mini
  use_repo_map: true
  use_system_prompt: false
  use_temperature: false
  editor_model_name: azure/gpt-4o
  editor_edit_format: editor-diff

- name: o1-mini
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  use_system_prompt: false
  use_temperature: false
  editor_model_name: gpt-4o
  editor_edit_format: editor-diff

- name: openai/o1-preview
  edit_format: diff
  weak_model_name: openai/gpt-4o-mini
  use_repo_map: true
  use_system_prompt: false
  use_temperature: false
  editor_model_name: openai/gpt-4o
  editor_edit_format: editor-diff

- name: azure/o1-preview
  edit_format: diff
  weak_model_name: azure/gpt-4o-mini
  use_repo_map: true
  use_system_prompt: false
  use_temperature: false
  editor_model_name: azure/gpt-4o
  editor_edit_format: editor-diff

- name: azure/o1
  edit_format: diff
  weak_model_name: azure/gpt-4o-mini
  use_repo_map: true
  use_temperature: false
  streaming: false
  editor_model_name: azure/gpt-4o
  editor_edit_format: editor-diff
  accepts_settings: ["reasoning_effort"]

- name: o1-preview
  edit_format: architect
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  use_system_prompt: false
  use_temperature: false
  editor_model_name: gpt-4o
  editor_edit_format: editor-diff

- name: openrouter/openai/o1-mini
  weak_model_name: openrouter/openai/gpt-4o-mini
  use_repo_map: true
  use_system_prompt: false
  use_temperature: false
  streaming: false
  editor_model_name: openrouter/openai/gpt-4o
  editor_edit_format: editor-diff

- name: openrouter/openai/o1-preview
  edit_format: diff
  weak_model_name: openrouter/openai/gpt-4o-mini
  use_repo_map: true
  use_system_prompt: false
  use_temperature: false
  streaming: false
  editor_model_name: openrouter/openai/gpt-4o
  editor_edit_format: editor-diff

- name: openrouter/openai/o1
  edit_format: diff
  weak_model_name: openrouter/openai/gpt-4o-mini
  use_repo_map: true
  use_temperature: false
  streaming: false
  editor_model_name: openrouter/openai/gpt-4o
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]

- name: openai/o1
  edit_format: diff
  weak_model_name: openai/gpt-4o-mini
  use_repo_map: true
  use_temperature: false
  streaming: false
  editor_model_name: openai/gpt-4o
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]

- name: o1
  edit_format: diff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  use_temperature: false
  streaming: false
  editor_model_name: gpt-4o
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]

- name: openrouter/qwen/qwen-2.5-coder-32b-instruct
  edit_format: diff
  weak_model_name: openrouter/qwen/qwen-2.5-coder-32b-instruct
  use_repo_map: true
  editor_model_name: openrouter/qwen/qwen-2.5-coder-32b-instruct
  editor_edit_format: editor-diff

- name: openrouter/deepseek/deepseek-r1-distill-llama-70b
  edit_format: diff
  weak_model_name: openrouter/deepseek/deepseek-chat
  use_repo_map: true
  examples_as_sys_msg: true
  extra_params:
    max_tokens: 8192
  caches_by_default: true
  use_temperature: false
  editor_model_name: openrouter/deepseek/deepseek-chat
  editor_edit_format: editor-diff

- name: fireworks_ai/accounts/fireworks/models/deepseek-r1
  edit_format: diff
  weak_model_name: fireworks_ai/accounts/fireworks/models/deepseek-v3
  use_repo_map: true
  use_temperature: false
  streaming: true
  editor_model_name: fireworks_ai/accounts/fireworks/models/deepseek-v3
  editor_edit_format: editor-diff
  reasoning_tag: think
  extra_params:
      max_tokens: 160000

- name: fireworks_ai/accounts/fireworks/models/deepseek-v3
  edit_format: diff
  use_repo_map: true
  reminder: sys
  examples_as_sys_msg: true
  extra_params:
      max_tokens: 128000

- name: fireworks_ai/accounts/fireworks/models/deepseek-v3-0324
  edit_format: diff
  use_repo_map: true
  reminder: sys
  examples_as_sys_msg: true
  extra_params:
      max_tokens: 160000

- name: openai/o3-mini
  edit_format: diff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: gpt-4o
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]

- name: o3-mini
  edit_format: diff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: gpt-4o
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]

- name: openrouter/openai/o3-mini
  edit_format: diff
  weak_model_name: openrouter/openai/gpt-4o-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: openrouter/openai/gpt-4o
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]

- name: openrouter/openai/o3-mini-high
  edit_format: diff
  weak_model_name: openrouter/openai/gpt-4o-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: openrouter/openai/gpt-4o
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]

- name: azure/o3-mini
  edit_format: diff
  weak_model_name: azure/gpt-4o-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: azure/gpt-4o
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]

- name: gpt-4.5-preview
  edit_format: diff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  lazy: true
  reminder: sys
  examples_as_sys_msg: true
  editor_model_name: gpt-4o
  editor_edit_format: editor-diff

- name: openai/gpt-4.5-preview
  edit_format: diff
  weak_model_name: gpt-4o-mini
  use_repo_map: true
  lazy: true
  reminder: sys
  examples_as_sys_msg: true
  editor_model_name: openai/gpt-4o
  editor_edit_format: editor-diff

- name: fireworks_ai/accounts/fireworks/models/qwq-32b
  reasoning_tag: think
  edit_format: diff
  weak_model_name: fireworks_ai/accounts/fireworks/models/qwen2p5-coder-32b-instruct
  use_repo_map: true
  editor_model_name: fireworks_ai/accounts/fireworks/models/qwen2p5-coder-32b-instruct
  editor_edit_format: editor-diff
  reminder: user
  examples_as_sys_msg: true
  use_temperature: 0.6
  extra_params:
      max_tokens: 32000
      top_p: 0.95

- name: groq/qwen-qwq-32b
  reasoning_tag: think
  edit_format: diff
  weak_model_name: groq/qwen-2.5-coder-32b
  use_repo_map: true
  editor_model_name: groq/qwen-2.5-coder-32b
  editor_edit_format: editor-diff
  use_temperature: 0.6
  extra_params:
      max_tokens: 128000
      top_p: 0.95

- name: cohere_chat/command-a-03-2025
  examples_as_sys_msg: true

- name: openrouter/cohere/command-a-03-2025
  examples_as_sys_msg: true

- name: gemini/gemma-3-27b-it
  use_system_prompt: false

- name: openrouter/google/gemma-3-27b-it:free
  use_system_prompt: false

- name: openrouter/google/gemma-3-27b-it
  use_system_prompt: false

- name: gemini/gemini-2.5-pro-preview-03-25
  overeager: true
  edit_format: diff-fenced
  use_repo_map: true
  weak_model_name: gemini/gemini-2.0-flash

- name: gemini/gemini-2.5-pro-exp-03-25
  edit_format: diff-fenced
  use_repo_map: true
  overeager: true
  weak_model_name: gemini/gemini-2.5-flash-preview-04-17

- name: openrouter/google/gemini-2.5-pro-exp-03-25
  edit_format: diff-fenced
  overeager: true
  use_repo_map: true
  weak_model_name: openrouter/google/gemini-2.0-flash-exp:free

- name: vertex_ai/gemini-2.5-pro-exp-03-25
  edit_format: diff-fenced
  use_repo_map: true
  weak_model_name: vertex_ai/gemini-2.5-flash-preview-04-17
  overeager: true
  editor_model_name: vertex_ai/gemini-2.5-flash-preview-04-17

- name: vertex_ai/gemini-2.5-pro-preview-03-25
  edit_format: diff-fenced
  use_repo_map: true
  weak_model_name: vertex_ai/gemini-2.5-flash-preview-04-17
  overeager: true
  editor_model_name: vertex_ai/gemini-2.5-flash-preview-04-17

- name: openrouter/openrouter/quasar-alpha
  use_repo_map: true
  edit_format: diff
  examples_as_sys_msg: true

- name: openrouter/x-ai/grok-3-beta
  use_repo_map: true
  edit_format: diff

- name: xai/grok-3-beta
  use_repo_map: true
  edit_format: diff

- name: openrouter/x-ai/grok-3-mini-beta
  use_repo_map: true
  edit_format: whole
  accepts_settings:
    - reasoning_effort 
  #extra_params:
  #  extra_body:
  #    reasoning_effort: high

- name: openai/o4-mini
  edit_format: diff
  weak_model_name: openai/gpt-4.1-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: openai/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #   reasoning_effort: high

- name: openrouter/openai/o4-mini
  edit_format: diff
  weak_model_name: openrouter/openai/gpt-4.1-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: openrouter/openai/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #   reasoning_effort: high

- name: azure/o4-mini
  edit_format: diff
  weak_model_name: azure/gpt-4.1-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: azure/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #   reasoning_effort: high

- name: xai/grok-3-mini-beta
  use_repo_map: true
  edit_format: whole
  accepts_settings:
    - reasoning_effort
  #extra_params:
  #  extra_body:
  #    reasoning_effort: low

- name: openrouter/x-ai/grok-3-fast-beta
  use_repo_map: true
  edit_format: diff

- name: xai/grok-3-fast-beta
  use_repo_map: true
  edit_format: diff

- name: openrouter/x-ai/grok-3-mini-fast-beta
  use_repo_map: true
  edit_format: whole
  accepts_settings:
    - reasoning_effort

- name: xai/grok-3-mini-fast-beta
  use_repo_map: true
  edit_format: whole
  accepts_settings:
    - reasoning_effort

- name: openrouter/openrouter/optimus-alpha
  use_repo_map: true
  edit_format: diff
  examples_as_sys_msg: true
  
- name: gpt-4.1
  edit_format: diff
  weak_model_name: gpt-4.1-mini
  use_repo_map: true
  reminder: sys # user: 52.x%/96.9%
  examples_as_sys_msg: false # true: 51.6% correct, 95.6% well formed; false: 52.4%/98.2%
  editor_model_name: gpt-4.1-mini

- name: openai/gpt-4.1
  edit_format: diff
  weak_model_name: openai/gpt-4.1-mini
  use_repo_map: true
  reminder: sys
  examples_as_sys_msg: false
  editor_model_name: openai/gpt-4.1-mini

- name: azure/gpt-4.1
  edit_format: diff
  weak_model_name: azure/gpt-4.1-mini
  use_repo_map: true
  reminder: sys
  examples_as_sys_msg: false
  editor_model_name: azure/gpt-4.1-mini

- name: openrouter/openai/gpt-4.1
  edit_format: diff
  weak_model_name: openrouter/openai/gpt-4.1-mini
  use_repo_map: true
  reminder: sys
  examples_as_sys_msg: false
  editor_model_name: openrouter/openai/gpt-4.1-mini

- name: gpt-4.1-mini
  edit_format: diff
  use_repo_map: true
  reminder: sys
  examples_as_sys_msg: false # false: 32.x%/92.4% (60+ malformed responses); true: 31.7/90.2/60+

- name: openai/gpt-4.1-mini
  edit_format: diff
  use_repo_map: true
  reminder: sys
  examples_as_sys_msg: false

- name: azure/gpt-4.1-mini
  edit_format: diff
  use_repo_map: true
  reminder: sys
  examples_as_sys_msg: false

- name: openrouter/openai/gpt-4.1-mini
  edit_format: diff
  use_repo_map: true
  reminder: sys
  examples_as_sys_msg: false

- name: o3
  streaming: false
  edit_format: diff
  weak_model_name: gpt-4.1-mini
  use_repo_map: true
  editor_model_name: gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #    reasoning_effort: high

- name: o3-pro
  streaming: false
  edit_format: diff
  weak_model_name: gpt-4.1-mini
  use_repo_map: true
  editor_model_name: gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #    reasoning_effort: high

- name: openai/o4-mini
  edit_format: diff
  weak_model_name: openai/gpt-4.1-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: openai/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #   reasoning_effort: high

- name: openrouter/openai/o4-mini
  edit_format: diff
  weak_model_name: openrouter/openai/gpt-4.1-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: openrouter/openai/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #   reasoning_effort: high

- name: azure/o4-mini
  edit_format: diff
  weak_model_name: azure/gpt-4.1-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: azure/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #   reasoning_effort: high

- name: openai/o3
  streaming: false
  edit_format: diff
  weak_model_name: openai/gpt-4.1-mini
  use_repo_map: true
  editor_model_name: openai/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #    reasoning_effort: high

- name: openai/o3-pro
  streaming: false
  edit_format: diff
  weak_model_name: openai/gpt-4.1-mini
  use_repo_map: true
  editor_model_name: openai/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #    reasoning_effort: high

- name: openai/o4-mini
  edit_format: diff
  weak_model_name: openai/gpt-4.1-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: openai/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #   reasoning_effort: high

- name: openrouter/openai/o4-mini
  edit_format: diff
  weak_model_name: openrouter/openai/gpt-4.1-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: openrouter/openai/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #   reasoning_effort: high

- name: azure/o4-mini
  edit_format: diff
  weak_model_name: azure/gpt-4.1-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: azure/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #   reasoning_effort: high

- name: openrouter/openai/o3
  streaming: false
  edit_format: diff
  weak_model_name: openrouter/openai/gpt-4.1-mini
  use_repo_map: true
  editor_model_name: openrouter/openai/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #    reasoning_effort: high

- name: openrouter/openai/o3-pro
  streaming: false
  edit_format: diff
  weak_model_name: openrouter/openai/gpt-4.1-mini
  use_repo_map: true
  editor_model_name: openrouter/openai/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #    reasoning_effort: high

- name: openai/o4-mini
  edit_format: diff
  weak_model_name: openai/gpt-4.1-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: openai/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #   reasoning_effort: high

- name: openrouter/openai/o4-mini
  edit_format: diff
  weak_model_name: openrouter/openai/gpt-4.1-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: openrouter/openai/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #   reasoning_effort: high

- name: azure/o4-mini
  edit_format: diff
  weak_model_name: azure/gpt-4.1-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: azure/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #   reasoning_effort: high

- name: azure/o3
  streaming: false
  edit_format: diff
  weak_model_name: azure/gpt-4.1-mini
  use_repo_map: true
  editor_model_name: azure/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #    reasoning_effort: high

- name: azure/o3-pro
  streaming: false
  edit_format: diff
  weak_model_name: azure/gpt-4.1-mini
  use_repo_map: true
  editor_model_name: azure/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #    reasoning_effort: high

- name: openai/o4-mini
  edit_format: diff
  weak_model_name: openai/gpt-4.1-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: openai/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true

- name: openrouter/openai/o4-mini
  edit_format: diff
  weak_model_name: openrouter/openai/gpt-4.1-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: openrouter/openai/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true

- name: azure/o4-mini
  edit_format: diff
  weak_model_name: azure/gpt-4.1-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: azure/gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true

- name: o4-mini
  edit_format: diff
  weak_model_name: gpt-4.1-mini
  use_repo_map: true
  use_temperature: false
  editor_model_name: gpt-4.1
  editor_edit_format: editor-diff
  system_prompt_prefix: "Formatting re-enabled. "
  accepts_settings: ["reasoning_effort"]
  examples_as_sys_msg: true
  #extra_params:
  #  extra_body:
  #   reasoning_effort: high
      
- name: gemini/gemini-2.5-flash-preview-04-17
  edit_format: diff
  use_repo_map: true
  accepts_settings: ["reasoning_effort", "thinking_tokens"]

- name: gemini-2.5-flash-preview-04-17
  edit_format: diff
  use_repo_map: true
  accepts_settings: ["reasoning_effort", "thinking_tokens"]

- name: vertex_ai/gemini-2.5-flash-preview-04-17
  edit_format: diff
  use_repo_map: true
  accepts_settings: ["reasoning_effort", "thinking_tokens"]

- name: openrouter/google/gemini-2.5-pro-preview-03-25
  overeager: true
  edit_format: diff-fenced
  use_repo_map: true
  weak_model_name: openrouter/google/gemini-2.0-flash-001
    
- name: gemini/gemini-2.5-pro-preview-05-06
  overeager: true
  edit_format: diff-fenced
  use_repo_map: true
  weak_model_name: gemini/gemini-2.5-flash-preview-04-17

- name: gemini/gemini-2.5-pro-preview-06-05
  overeager: true
  edit_format: diff-fenced
  use_repo_map: true
  weak_model_name: gemini/gemini-2.5-flash-preview-04-17
  accepts_settings: ["thinking_tokens"]

- name: gemini/gemini-2.5-flash
  overeager: true
  edit_format: diff-fenced
  use_repo_map: true
  use_temperature: false
  accepts_settings: ["thinking_tokens"]

- name: gemini/gemini-2.5-pro
  overeager: true
  edit_format: diff-fenced
  use_repo_map: true
  weak_model_name: gemini/gemini-2.5-flash
  use_temperature: false
  accepts_settings: ["thinking_tokens"]

- name: vertex_ai/gemini-2.5-pro-preview-05-06
  edit_format: diff-fenced
  use_repo_map: true
  weak_model_name: vertex_ai/gemini-2.5-flash-preview-04-17
  overeager: true
  editor_model_name: vertex_ai/gemini-2.5-flash-preview-04-17

- name: vertex_ai/gemini-2.5-pro-preview-06-05
  edit_format: diff-fenced
  use_repo_map: true
  weak_model_name: vertex_ai/gemini-2.5-flash-preview-04-17
  overeager: true
  editor_model_name: vertex_ai/gemini-2.5-flash-preview-04-17
  accepts_settings: ["thinking_tokens"]

- name: openrouter/google/gemini-2.5-pro-preview-05-06
  overeager: true
  edit_format: diff-fenced
  use_repo_map: true
  weak_model_name: openrouter/google/gemini-2.0-flash-001

- name: openrouter/google/gemini-2.5-pro-preview-06-05
  overeager: true
  edit_format: diff-fenced
  use_repo_map: true
  weak_model_name: openrouter/google/gemini-2.0-flash-001
  accepts_settings: ["thinking_tokens"]

- name: openrouter/google/gemini-2.5-pro
  overeager: true
  edit_format: diff-fenced
  use_repo_map: true
  weak_model_name: openrouter/google/gemini-2.5-flash
  accepts_settings: ["thinking_tokens"]

#- name: openrouter/qwen/qwen3-235b-a22b
#  system_prompt_prefix: "/no_think"
#  use_temperature: 0.7
#  extra_params:
#    max_tokens: 24000
#    top_p: 0.8
#    top_k: 20
#    min_p: 0.0
#    temperature: 0.7
#    extra_body:
#      provider:
#        order: ["Together"]

#- name: together_ai/Qwen/Qwen3-235B-A22B-fp8-tput
#  system_prompt_prefix: "/no_think"
#  use_temperature: 0.7
#  reasoning_tag: think
#  extra_params:
#    max_tokens: 24000
#    top_p: 0.8
#    top_k: 20
#    min_p: 0.0
#    temperature: 0.7  


- name: claude-sonnet-4-20250514
  edit_format: diff
  weak_model_name: claude-3-5-haiku-20241022
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: claude-sonnet-4-20250514
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: anthropic/claude-sonnet-4-20250514
  edit_format: diff
  weak_model_name: anthropic/claude-3-5-haiku-20241022
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: anthropic/claude-sonnet-4-20250514
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: bedrock/anthropic.claude-sonnet-4-20250514-v1:0
  edit_format: diff
  weak_model_name: bedrock/anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: bedrock/anthropic.claude-sonnet-4-20250514-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: bedrock/us.anthropic.claude-sonnet-4-20250514-v1:0
  edit_format: diff
  weak_model_name: bedrock/us.anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: bedrock/us.anthropic.claude-sonnet-4-20250514-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: bedrock_converse/anthropic.claude-sonnet-4-20250514-v1:0
  edit_format: diff
  weak_model_name: bedrock_converse/anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: bedrock_converse/anthropic.claude-sonnet-4-20250514-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: bedrock_converse/us.anthropic.claude-sonnet-4-20250514-v1:0
  edit_format: diff
  weak_model_name: bedrock_converse/us.anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: bedrock_converse/us.anthropic.claude-sonnet-4-20250514-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: vertex_ai/claude-sonnet-4@20250514
  edit_format: diff
  weak_model_name: vertex_ai/claude-3-5-haiku@20241022
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    max_tokens: 64000
  editor_model_name: vertex_ai/claude-sonnet-4@20250514
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: vertex_ai/claude-sonnet-4@20250514
  edit_format: diff
  weak_model_name: vertex_ai/claude-3-5-haiku@20241022
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    max_tokens: 64000
  editor_model_name: vertex_ai/claude-sonnet-4@20250514
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: openrouter/anthropic/claude-sonnet-4
  edit_format: diff
  weak_model_name: openrouter/anthropic/claude-3-5-haiku
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: openrouter/anthropic/claude-sonnet-4
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: anthropic.claude-sonnet-4-20250514-v1:0
  edit_format: diff
  weak_model_name: anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: anthropic.claude-sonnet-4-20250514-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: bedrock_converse/eu.anthropic.claude-sonnet-4-20250514-v1:0
  edit_format: diff
  weak_model_name: bedrock_converse/eu.anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: bedrock_converse/eu.anthropic.claude-sonnet-4-20250514-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: eu.anthropic.claude-sonnet-4-20250514-v1:0
  edit_format: diff
  weak_model_name: eu.anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: eu.anthropic.claude-sonnet-4-20250514-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: us.anthropic.claude-sonnet-4-20250514-v1:0
  edit_format: diff
  weak_model_name: us.anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 64000
  cache_control: true
  editor_model_name: us.anthropic.claude-sonnet-4-20250514-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: claude-opus-4-20250514
  edit_format: diff
  weak_model_name: claude-3-5-haiku-20241022
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 32000
  cache_control: true
  editor_model_name: claude-sonnet-4-20250514
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: anthropic/claude-opus-4-20250514
  edit_format: diff
  weak_model_name: anthropic/claude-3-5-haiku-20241022
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 32000
  cache_control: true
  editor_model_name: anthropic/claude-sonnet-4-20250514
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: anthropic.claude-opus-4-20250514-v1:0
  edit_format: diff
  weak_model_name: anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 32000
  cache_control: true
  editor_model_name: anthropic.claude-sonnet-4-20250514-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: bedrock_converse/anthropic.claude-opus-4-20250514-v1:0
  edit_format: diff
  weak_model_name: bedrock_converse/anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 32000
  cache_control: true
  editor_model_name: bedrock_converse/anthropic.claude-sonnet-4-20250514-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: bedrock_converse/us.anthropic.claude-opus-4-20250514-v1:0
  edit_format: diff
  weak_model_name: bedrock_converse/us.anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 32000
  cache_control: true
  editor_model_name: bedrock_converse/us.anthropic.claude-sonnet-4-20250514-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: bedrock_converse/eu.anthropic.claude-opus-4-20250514-v1:0
  edit_format: diff
  weak_model_name: bedrock_converse/eu.anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 32000
  cache_control: true
  editor_model_name: bedrock_converse/eu.anthropic.claude-sonnet-4-20250514-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: eu.anthropic.claude-opus-4-20250514-v1:0
  edit_format: diff
  weak_model_name: eu.anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 32000
  cache_control: true
  editor_model_name: eu.anthropic.claude-sonnet-4-20250514-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: us.anthropic.claude-opus-4-20250514-v1:0
  edit_format: diff
  weak_model_name: us.anthropic.claude-3-5-haiku-20241022-v1:0
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 32000
  cache_control: true
  editor_model_name: us.anthropic.claude-sonnet-4-20250514-v1:0
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: vertex_ai/claude-opus-4@20250514
  edit_format: diff
  weak_model_name: vertex_ai/claude-3-5-haiku@20241022
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    max_tokens: 32000
  editor_model_name: vertex_ai/claude-sonnet-4@20250514
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: vertex_ai/claude-opus-4@20250514
  edit_format: diff
  weak_model_name: vertex_ai/claude-3-5-haiku@20241022
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    max_tokens: 32000
  editor_model_name: vertex_ai/claude-sonnet-4@20250514
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

- name: vertex_ai/gemini-2.5-flash-preview-05-20
  edit_format: diff
  use_repo_map: true
  accepts_settings: ["reasoning_effort", "thinking_tokens"]

- name: openrouter/anthropic/claude-opus-4
  edit_format: diff
  weak_model_name: openrouter/anthropic/claude-3-5-haiku
  use_repo_map: true
  examples_as_sys_msg: false
  extra_params:
    extra_headers:
      anthropic-beta: prompt-caching-2024-07-31,pdfs-2024-09-25,output-128k-2025-02-19
    max_tokens: 32000
  cache_control: true
  editor_model_name: openrouter/anthropic/claude-sonnet-4
  editor_edit_format: editor-diff
  accepts_settings: ["thinking_tokens"]

